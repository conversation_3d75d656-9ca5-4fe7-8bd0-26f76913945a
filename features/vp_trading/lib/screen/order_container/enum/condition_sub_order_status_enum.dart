import 'package:flutter/material.dart';
import 'package:vp_core/theme/bloc/theme_cubit.dart';

enum ConditionSubOrderStatusEnum {
  waitingToSend,
  waiting,
  partialMatch,
  sent,
  fullMatch,
  edited,
  canceled,
  rejected,
  ineffective,
}

extension ConditionSubOrderStatusEnumExt on ConditionSubOrderStatusEnum {
  String get title {
    switch (this) {
      case ConditionSubOrderStatusEnum.waitingToSend:
        return "Chờ gửi";
      case ConditionSubOrderStatusEnum.waiting:
        return "Đang chờ";
      case ConditionSubOrderStatusEnum.partialMatch:
        return "Khớp 1 phần";
      case ConditionSubOrderStatusEnum.sent:
        return "Đã gửi";
      case ConditionSubOrderStatusEnum.fullMatch:
        return "Khớp hết";
      case ConditionSubOrderStatusEnum.edited:
        return "Đã sửa";
      case ConditionSubOrderStatusEnum.canceled:
        return "Đã hủy";
      case ConditionSubOrderStatusEnum.rejected:
        return "Từ chối";
      case ConditionSubOrderStatusEnum.ineffective:
        return "Hết hiệu lực";
    }
  }

  Color get color {
    switch (this) {
      case ConditionSubOrderStatusEnum.waitingToSend:
      case ConditionSubOrderStatusEnum.waiting:
      case ConditionSubOrderStatusEnum.sent:
      case ConditionSubOrderStatusEnum.partialMatch:
        return vpColor.backgroundAccentBlue;
      case ConditionSubOrderStatusEnum.fullMatch:
        return vpColor.backgroundAccentGreen;
      case ConditionSubOrderStatusEnum.edited:
      case ConditionSubOrderStatusEnum.canceled:
      case ConditionSubOrderStatusEnum.rejected:
      case ConditionSubOrderStatusEnum.ineffective:
        return vpColor.backgroundAccentRed;
    }
  }

  Color get textColor {
    switch (this) {
      case ConditionSubOrderStatusEnum.waitingToSend:
      case ConditionSubOrderStatusEnum.waiting:
      case ConditionSubOrderStatusEnum.sent:
      case ConditionSubOrderStatusEnum.partialMatch:
        return vpColor.textAccentBlue;
      case ConditionSubOrderStatusEnum.fullMatch:
        return vpColor.textAccentGreen;
      case ConditionSubOrderStatusEnum.edited:
      case ConditionSubOrderStatusEnum.canceled:
      case ConditionSubOrderStatusEnum.rejected:
      case ConditionSubOrderStatusEnum.ineffective:
        return vpColor.textAccentRed;
    }
  }

  static ConditionSubOrderStatusEnum conditionSubOrderStatusFromString(
    String? code,
  ) {
    switch (code?.toUpperCase()) {
      case "WAITING_SEND":
        return ConditionSubOrderStatusEnum.waitingToSend;
      case "SENDING":
      case "CANCELLING":
      case "MODIFYING":
        return ConditionSubOrderStatusEnum.waiting;
      case "SENTED":
        return ConditionSubOrderStatusEnum.sent;
      case "PARTY_TRADE":
        return ConditionSubOrderStatusEnum.partialMatch;
      case "TRADED":
        return ConditionSubOrderStatusEnum.fullMatch;
      case "MODIFIED":
        return ConditionSubOrderStatusEnum.edited;
      case "CANCELLED":
        return ConditionSubOrderStatusEnum.canceled;
      case "REJECTED":
        return ConditionSubOrderStatusEnum.rejected;
      case "EXPIRE":
        return ConditionSubOrderStatusEnum.ineffective;
      default:
        return ConditionSubOrderStatusEnum.waiting;
    }
  }
}
