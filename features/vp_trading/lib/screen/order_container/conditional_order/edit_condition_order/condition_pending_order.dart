import 'package:flutter/material.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_stock_common/vp_stock_common.dart';
import 'package:vp_trading/core/constant/order_type.dart';
import 'package:vp_trading/cubit/order_edit/edit_condition_order_cubit.dart';
import 'package:vp_trading/cubit/place_order/available_trade/available_trade_cubit.dart';
import 'package:vp_trading/cubit/place_order/order_suggest/order_suggest_cubit.dart';
import 'package:vp_trading/cubit/place_order/place_order/place_order_cubit.dart';
import 'package:vp_trading/cubit/place_order/stock_info/stock_info_cubit.dart';
import 'package:vp_trading/cubit/place_order/validate_condition_order/validate_condition_order_cubit.dart';
import 'package:vp_trading/cubit/place_order/validate_order/validate_order_cubit.dart';
import 'package:vp_trading/model/order/condition_order_book/condition_order_book_model.dart';
import 'package:vp_trading/screen/order_container/conditional_order/edit_condition_order/edit_awaiting_order_widget.dart';
import 'package:vp_trading/screen/order_container/enum/order_type_enum.dart';

void opendEditConditionPendingOrderBottomSheet({
  required BuildContext context,
  required ConditionOrderBookModel item,
  required VoidCallback onEditSuccess,
}) {
  VPPopup.bottomSheet(
    MultiBlocProvider(
      providers: [
        BlocProvider(create: (context) => EditConditionOrderCubit()),
        BlocProvider(create: (context) => ValidateConditionOrderCubit()),
        BlocProvider(create: (context) => ValidateOrderCubit()),
        BlocProvider(create: (context) => OrderSuggestCubit()),
        BlocProvider(
          create:
              (context) => PlaceOrderCubit(
                symbol: item.symbol ?? '',
                action:
                    item.orderTypeEnum == OrderTypeEnum.buy
                        ? OrderAction.buy
                        : OrderAction.sell,

                accountModel:
                    GetIt.instance<SubAccountCubit>()
                            .subAccountsAllHaveDerivative
                            .where((e) => e.id == item.accountId)
                            .isNotEmpty
                        ? GetIt.instance<SubAccountCubit>()
                            .subAccountsAllHaveDerivative
                            .where((e) => e.id == item.accountId)
                            .first
                        : null,

                subAccountType:
                    GetIt.instance<SubAccountCubit>()
                            .subAccountsAllHaveDerivative
                            .where((e) => e.id == item.accountId)
                            .isNotEmpty
                        ? GetIt.instance<SubAccountCubit>()
                            .subAccountsAllHaveDerivative
                            .where((e) => e.id == item.accountId)
                            .first
                            .toSubAccountType
                        : SubAccountType.normal,
              ),
        ),
        BlocProvider(create: (context) => StockInfoCubit()),
        BlocProvider(
          create:
              (context) =>
                  AvailableTradeCubit()..getAvailableTrade(
                    accountId: item.accountId ?? "",
                    symbol: item.symbol ?? "",
                  ),
        ),
      ],
      child: _EditConditionPendingOrder(
        item: item,
        onEditSuccess: onEditSuccess,
      ),
    ),
  ).copyWith(padding: EdgeInsets.zero).showSheet(context);
}

class _EditConditionPendingOrder extends StatefulWidget {
  const _EditConditionPendingOrder({
    required this.item,
    required this.onEditSuccess,
  });

  final ConditionOrderBookModel item;
  final VoidCallback onEditSuccess;

  @override
  State<_EditConditionPendingOrder> createState() =>
      _EditConditionPendingOrderState();
}

class _EditConditionPendingOrderState
    extends State<_EditConditionPendingOrder> {
  @override
  void initState() {
    super.initState();

    // Initialize stock info for the symbol
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (widget.item.symbol != null) {
        context.read<StockInfoCubit>().loadData(widget.item.symbol!);
      }

      // Set order type to waiting for OrderSuggest to work correctly
      context.read<PlaceOrderCubit>().updateOrderType(OrderType.waiting);
    });
  }

  @override
  Widget build(BuildContext context) {
    return MultiBlocListener(
      listeners: [
        // Listen to AvailableTradeCubit and update ValidateOrderCubit
        BlocListener<AvailableTradeCubit, AvailableTradeState>(
          listenWhen:
              (previous, current) =>
                  previous.availableTrade != current.availableTrade,
          listener: (context, state) {
            context.read<ValidateOrderCubit>().updateParam(
              availableTrade: state.availableTrade,
            );
          },
        ),
        // Listen to StockInfoCubit and update ValidateOrderCubit
        BlocListener<StockInfoCubit, StockInfoState>(
          listenWhen:
              (previous, current) =>
                  previous.stockInfo == null && current.stockInfo != null,
          listener: (context, state) {
            context.read<ValidateOrderCubit>().updateParam(
              stockInfo: state.stockInfo,
              action:
                  widget.item.orderTypeEnum == OrderTypeEnum.buy
                      ? OrderAction.buy
                      : OrderAction.sell,
              orderType: OrderType.waiting,
            );

            // Initialize OrderSuggestCubit for waiting orders
            final orderSuggestCubit = context.read<OrderSuggestCubit>();
            orderSuggestCubit.stockInfo = state.stockInfo;
            orderSuggestCubit.orderType = OrderType.waiting;
            orderSuggestCubit.updateSuggestWaiting();
          },
        ),
      ],
      child: BlocBuilder<StockInfoCubit, StockInfoState>(
        builder: (context, state) {
          return GestureDetector(
            onTap: () => FocusScope.of(context).unfocus(),
            child: SingleChildScrollView(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          widget.item.orderTypeEnum == OrderTypeEnum.buy
                              ? "Lệnh chờ mua"
                              : "Lệnh chờ bán",
                          style: vpTextStyle.subtitle14.copyColor(
                            vpColor.textPrimary,
                          ),
                        ),
                        VPTextField(
                          hintText: widget.item.symbol ?? '',
                          textAlign: TextAlign.start,
                          inputType: InputType.disabled,
                        ),
                      ],
                    ),
                  ),

                  const SizedBox(height: 16),

                  // Edit Awaiting Order Widget
                  EditAwaitingOrderWidget(
                    item: widget.item,
                    onEditSuccess: widget.onEditSuccess,
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }
}
