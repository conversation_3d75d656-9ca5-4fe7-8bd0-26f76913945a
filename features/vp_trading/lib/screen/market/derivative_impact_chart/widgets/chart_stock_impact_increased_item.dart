import 'package:flutter/material.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/themes/vp_color_old.dart';
import 'package:vp_trading/model/market/stock_impact_chart_data_render.dart';
import 'package:vp_trading/screen/market/derivative_impact_chart/widgets/tooltip_widget.dart';

class ChartStockImpactIncreasedItem extends StatelessWidget {
  final StockImpactItemDataRender? data;

  final int index;

  const ChartStockImpactIncreasedItem({
    super.key,
    this.data,
    required this.index,
  });

  double? get _heightColumnChart => data?.valueForChart?.toDouble();

  String get _symbol => data?.symbol ?? "";

  AxisDirection get _preferredDirection =>
      index <= 4 ? AxisDirection.right : AxisDirection.left;

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: 24,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          ToolTipWidget(
            preferredDirection: _preferredDirection,
            capitalization: data?.capitalization,
            contributionLevel: data?.contributionLevel,
            child: Container(
              decoration: BoxDecoration(
                color: vpColor.strokeGreen,
                borderRadius: const BorderRadius.all(Radius.circular(4)),
              ),
              width: 16,
              height: _heightColumnChart,
            ),
          ),
          Text(
            _symbol,
            textAlign: TextAlign.end,
            style: TextStyle(
              fontWeight: FontWeight.w500,
              color: themeData.gray500,
              fontSize: 8,
              height: 2,
            ),
          ),
        ],
      ),
    );
  }
}
