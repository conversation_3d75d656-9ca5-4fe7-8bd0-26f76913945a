import 'package:flutter/material.dart';
import 'package:just_the_tooltip/just_the_tooltip.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';

class ToolTipWidget extends StatelessWidget {
  final Widget child;
  final AxisDirection preferredDirection;
  final String? contributionLevel;
  final String? capitalization;
  final bool isIncreaseToolTip;

  const ToolTipWidget({
    super.key,
    required this.child,
    required this.preferredDirection,
    this.contributionLevel,
    this.capitalization,
    this.isIncreaseToolTip = true,
  });

  Color getImpactValueColor() {
    if (isIncreaseToolTip) {
      return vpColor.strokeGreen;
    } else {
      return vpColor.strokeDanger;
    }
  }

  @override
  Widget build(BuildContext context) {
    final style = vpTextStyle.captionMedium.copyColor(vpColor.textPrimary);

    return JustTheTooltip(
      triggerMode: TooltipTriggerMode.tap,
      preferredDirection: preferredDirection,
      tailBaseWidth: 8,
      tailLength: 8,
      content: DecoratedBox(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(4.0),
          color: vpColor.backgroundElevation1,
        ),
        child: Padding(
          padding: const EdgeInsets.fromLTRB(16, 8, 16, 8),
          child: IntrinsicWidth(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Text("Mức đóng góp: ", style: style),
                    Text(
                      contributionLevel ?? "",
                      style: style.copyColor(getImpactValueColor()),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                const VPDividerView(),
                const SizedBox(height: 8),

                Row(
                  children: [
                    Text("Vốn hoá: ", style: style),
                    Text(capitalization ?? "", style: style),
                    Text(" tỷ", style: style),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
      child: child,
    );
  }
}
