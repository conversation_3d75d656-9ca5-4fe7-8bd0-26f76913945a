import 'package:flutter/material.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_trading/model/market/stock_impact_chart_data_render.dart';
import 'package:vp_trading/screen/market/derivative_impact_chart/widgets/tooltip_widget.dart';

class ChartStockImpactDecreasedItem extends StatelessWidget {
  final StockImpactItemDataRender? data;

  final int index;

  const ChartStockImpactDecreasedItem({
    super.key,
    this.data,
    required this.index,
  });

  double? get _heightColumnChart => data?.valueForChart?.toDouble();

  String get _symbol => data?.symbol ?? "";

  AxisDirection get _preferredDirection =>
      index <= 4 ? AxisDirection.right : AxisDirection.left;

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: 24,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          Text(
            _symbol,
            textAlign: TextAlign.end,
            style: TextStyle(
              fontWeight: FontWeight.w500,
              color: themeData.gray500,
              fontSize: 8,
              height: 2,
            ),
          ),
          ToolTipWidget(
            preferredDirection: _preferredDirection,
            capitalization: data?.capitalization,
            contributionLevel: data?.contributionLevel,
            isIncreaseToolTip: false,
            child: Container(
              decoration: BoxDecoration(
                color: vpColor.strokeDanger,
                borderRadius: const BorderRadius.all(Radius.circular(4)),
              ),
              width: 16,
              height: _heightColumnChart,
            ),
          ),
        ],
      ),
    );
  }
}
