import 'package:flutter/material.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/custom_widget/animation/blink.dart';
import 'package:vp_trading/core/constant/constants.dart';
import 'package:vp_trading/core/extension/ext.dart';
import 'package:vp_trading/cubit/place_order/validate_order/validate_order_cubit.dart';
import 'package:vp_trading/generated/l10n.dart';
import 'package:vp_trading/screen/place_order/widgets/input_view/custom/input_field_box.dart';
import 'package:vp_trading/screen/place_order/widgets/input_view/custom/input_field_error.dart';
import 'package:vp_trading/utils/text_inputformater.dart';

class TakeProfitStopLossVolumeWidget extends StatefulWidget {
  final TextEditingController volumeController;

  const TakeProfitStopLossVolumeWidget({
    super.key,
    required this.volumeController,
  });

  @override
  State<TakeProfitStopLossVolumeWidget> createState() =>
      TakeProfitStopLossVolumeWidgetState();
}

class TakeProfitStopLossVolumeWidgetState
    extends State<TakeProfitStopLossVolumeWidget> {
  final ValueNotifier<bool> _volumeBlink = ValueNotifier(false);
  final _volumeFocusNode = FocusNode();

  void _focusListener() {
    if (_volumeFocusNode.hasFocus) {
      context.read<ValidateOrderCubit>().focusField(FocusKeyboard.volume);
    } else {
      context.read<ValidateOrderCubit>().focusField(FocusKeyboard.none);
    }
  }

  @override
  void initState() {
    super.initState();
    _volumeFocusNode.addListener(_focusListener);
  }

  @override
  void dispose() {
    _volumeFocusNode.dispose();
    _volumeBlink.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return MultiBlocListener(
      listeners: [
        BlocListener<ValidateOrderCubit, ValidateOrderState>(
          listenWhen:
              (previous, current) =>
                  previous.currentVolume != current.currentVolume,
          listener: (context, state) {
            if (state.currentVolume != null) {
              widget.volumeController.text = state.currentVolume!;
              widget.volumeController.selection = TextSelection.fromPosition(
                TextPosition(offset: widget.volumeController.text.length),
              );
            }
          },
        ),
        BlocListener<ValidateOrderCubit, ValidateOrderState>(
          listenWhen:
              (previous, current) =>
                  previous.focusKeyboard != current.focusKeyboard,
          listener: (context, state) {
            if (state.focusKeyboard == FocusKeyboard.volume) {
              _volumeFocusNode.requestFocus();
            }
          },
        ),
      ],
      child: BlocBuilder<ValidateOrderCubit, ValidateOrderState>(
        builder: (context, state) {
          return Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const SizedBox(height: 8),
                Text(
                  VPTradingLocalize.current.trading_volume,
                  style: vpTextStyle.subtitle14.copyColor(vpColor.textPrimary),
                ),
                const SizedBox(height: 4),
                ColoredBox(
                  color: vpColor.backgroundElevation0,
                  child: Blink(
                    blink: _volumeBlink,
                    child: InputFieldBox(
                      isError:
                          context.read<ValidateOrderCubit>().isEditMode
                              ? state.errorVolume.isEditOrderError
                              : state.errorVolume.isError &&
                                  widget.volumeController.text.isNotEmpty,

                      controller: widget.volumeController,
                      maxLength: 12,
                      hintText: 'KL',
                      onChange: (value) {
                        context.read<ValidateOrderCubit>().onChangeVolumne(
                          value,
                        );
                      },
                      focusNode: _volumeFocusNode,
                      scrollPadding: 180,
                      onTap: (increase) {
                        context.read<ValidateOrderCubit>().volumneTap(
                          text: widget.volumeController.text,
                          increase: increase,
                        );
                      },
                      inputFormatters: [
                        removeZeroStartInputFormatter,
                        ...volumeInputFormatter,
                      ],
                    ),
                  ),
                ),
                BlocBuilder<ValidateOrderCubit, ValidateOrderState>(
                  buildWhen:
                      (previous, current) =>
                          previous.errorVolume != current.errorVolume ||
                          previous.currentVolume != current.currentVolume,
                  builder: (context, state) {
                    return InputFieldError(
                      errorMessage: state.errorVolume.message(
                        context
                            .read<ValidateOrderCubit>()
                            .maxVolume()
                            .volumeString,
                      ),
                      text: widget.volumeController.text,
                      isShake: true,
                    );
                  },
                ),
                const SizedBox(height: 8),
              ],
            ),
          );
        },
      ),
    );
  }
}
