import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/custom_widget/animation/blink.dart';
import 'package:vp_trading/cubit/place_order/validate_condition_order/validate_condition_order_cubit.dart';
import 'package:vp_trading/screen/place_order/widgets/input_view/custom/input_field_box.dart';
import 'package:vp_trading/screen/place_order/widgets/input_view/custom/input_field_error.dart';
import 'package:vp_trading/utils/text_inputformater.dart';
import 'package:vp_trading/vp_trading.dart';

class TriggerTextInputField extends StatefulWidget {
  final TextEditingController triggerController;

  const TriggerTextInputField({super.key, required this.triggerController});

  @override
  State<TriggerTextInputField> createState() => TriggerTextInputFieldState();
}

class TriggerTextInputFieldState extends State<TriggerTextInputField> {
  final ValueNotifier<bool> _triggerBlink = ValueNotifier(false);
  final _triggerFocusNode = FocusNode();

  void _focusListener() {
    if (_triggerFocusNode.hasFocus) {
      context.read<ValidateConditionOrderCubit>().focusField(
        FocusKeyboard.trigger,
      );
    } else {
      context.read<ValidateConditionOrderCubit>().focusField(
        FocusKeyboard.none,
      );
    }
  }

  @override
  void initState() {
    super.initState();
    _triggerFocusNode.addListener(_focusListener);
    if (widget.triggerController.text.isNotEmpty) {
      context.read<ValidateConditionOrderCubit>().onChangeTrigger(
        widget.triggerController.text,
      );
    }
  }

  @override
  void dispose() {
    _triggerFocusNode.dispose();
    _triggerBlink.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return MultiBlocListener(
      listeners: [
        BlocListener<ValidateConditionOrderCubit, ValidateConditionOrderState>(
          listenWhen:
              (previous, current) =>
                  previous.currentTrigger != current.currentTrigger,
          listener: (context, state) {
            if (state.currentTrigger != null) {
              widget.triggerController.text = state.currentTrigger!;
              widget.triggerController.selection = TextSelection.fromPosition(
                TextPosition(offset: widget.triggerController.text.length),
              );
            }
          },
        ),

        BlocListener<ValidateConditionOrderCubit, ValidateConditionOrderState>(
          listenWhen:
              (previous, current) =>
                  previous.focusKeyboard != current.focusKeyboard,
          listener: (context, state) {
            if (state.focusKeyboard == FocusKeyboard.trigger) {
              _triggerFocusNode.requestFocus();
            }
          },
        ),
      ],
      child: BlocBuilder<
        ValidateConditionOrderCubit,
        ValidateConditionOrderState
      >(
        builder: (context, state) {
          return ClipRRect(
            borderRadius: BorderRadius.circular(4),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                ColoredBox(
                  color: vpColor.backgroundElevation0,
                  child: Blink(
                    blink: _triggerBlink,
                    child: InputFieldBox(
                      onTapField: () {
                        _triggerFocusNode.requestFocus();
                      },
                      isError:
                          context.read<ValidateConditionOrderCubit>().isEditMode
                              ? state.errorTrigger.isEditModeError
                              : state.errorTrigger.isError &&
                                  widget.triggerController.text.isNotEmpty,
                      controller: widget.triggerController,
                      hintText: '',
                      onChange: (value) {
                        context
                            .read<ValidateConditionOrderCubit>()
                            .onChangeTrigger(value);
                      },
                      focusNode: _triggerFocusNode,
                      onTap: (increase) {
                        context.read<ValidateConditionOrderCubit>().triggerTap(
                          text: widget.triggerController.text,
                          increase: increase,
                        );
                      },
                      inputFormatters: [
                        removeZeroStartInputFormatter,
                        LengthLimitingTextInputFormatter(8),
                        ...priceInputFormatter,
                      ],
                    ),
                  ),
                ),
                BlocBuilder<
                  ValidateConditionOrderCubit,
                  ValidateConditionOrderState
                >(
                  buildWhen:
                      (previous, current) =>
                          previous.errorTrigger != current.errorTrigger ||
                          previous.currentTrigger != current.currentTrigger,
                  builder: (context, state) {
                    return InputFieldError(
                      errorMessage: state.errorTrigger.message(
                        state.orderType.isStopLoss,
                      ),
                      text: widget.triggerController.text,
                      isShake: true,
                    );
                  },
                ),
                const SizedBox(height: 8),
              ],
            ),
          );
        },
      ),
    );
  }
}
