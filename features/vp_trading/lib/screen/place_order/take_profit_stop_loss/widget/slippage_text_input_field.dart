import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/custom_widget/animation/blink.dart';
import 'package:vp_trading/cubit/place_order/validate_condition_order/validate_condition_order_cubit.dart';
import 'package:vp_trading/screen/place_order/widgets/input_view/custom/input_field_box.dart';
import 'package:vp_trading/screen/place_order/widgets/input_view/custom/input_field_error.dart';
import 'package:vp_trading/utils/text_inputformater.dart';
import 'package:vp_trading/vp_trading.dart';

class SlippageTextInputField extends StatefulWidget {
  final TextEditingController slippageController;

  const SlippageTextInputField({super.key, required this.slippageController});

  @override
  State<SlippageTextInputField> createState() => SlippageTextInputFieldState();
}

class SlippageTextInputFieldState extends State<SlippageTextInputField> {
  final ValueNotifier<bool> _slippageBlink = ValueNotifier(false);
  final _slippageFocusNode = FocusNode();

  void _focusListener() {
    if (_slippageFocusNode.hasFocus) {
      context.read<ValidateConditionOrderCubit>().focusField(
        FocusKeyboard.slippage,
      );
    } else {
      context.read<ValidateConditionOrderCubit>().focusField(
        FocusKeyboard.none,
      );
    }
  }

  @override
  void initState() {
    super.initState();
    _slippageFocusNode.addListener(_focusListener);
    if (widget.slippageController.text.isNotEmpty) {
      context.read<ValidateConditionOrderCubit>().onChangeSlippage(
        widget.slippageController.text,
      );
    }
  }

  @override
  void dispose() {
    _slippageFocusNode.dispose();
    _slippageBlink.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return MultiBlocListener(
      listeners: [
        BlocListener<ValidateConditionOrderCubit, ValidateConditionOrderState>(
          listenWhen:
              (previous, current) =>
                  previous.currentSlippage != current.currentSlippage,
          listener: (context, state) {
            if (state.currentSlippage != null) {
              widget.slippageController.text = state.currentSlippage!;
              widget.slippageController.selection = TextSelection.fromPosition(
                TextPosition(offset: widget.slippageController.text.length),
              );
            }
          },
        ),

        BlocListener<ValidateConditionOrderCubit, ValidateConditionOrderState>(
          listenWhen:
              (previous, current) =>
                  previous.focusKeyboard != current.focusKeyboard,
          listener: (context, state) {
            if (state.focusKeyboard == FocusKeyboard.slippage) {
              _slippageFocusNode.requestFocus();
            }
          },
        ),
      ],
      child: BlocBuilder<
        ValidateConditionOrderCubit,
        ValidateConditionOrderState
      >(
        builder: (context, state) {
          return Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16.0),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(4),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    VPTradingLocalize.current.trading_condition_slippage_margin,
                    style: vpTextStyle.subtitle14.copyColor(
                      vpColor.textPrimary,
                    ),
                  ),
                  const SizedBox(height: 4),
                  ColoredBox(
                    color: vpColor.backgroundElevation0,
                    child: Blink(
                      blink: _slippageBlink,
                      child: InputFieldBox(
                        onTapField: () {
                          _slippageFocusNode.requestFocus();
                        },
                        isError:
                            context
                                    .read<ValidateConditionOrderCubit>()
                                    .isEditMode
                                ? state.errorSlippage.isEditModeError
                                : state.errorSlippage.isError &&
                                    widget.slippageController.text.isNotEmpty,
                        controller: widget.slippageController,
                        hintText: '',
                        onChange: (value) {
                          context
                              .read<ValidateConditionOrderCubit>()
                              .onChangeSlippage(value);
                        },
                        focusNode: _slippageFocusNode,
                        onTap: (increase) {
                          context
                              .read<ValidateConditionOrderCubit>()
                              .slippageTap(
                                text: widget.slippageController.text,
                                increase: increase,
                              );
                        },
                        inputFormatters: [
                          removeZeroStartInputFormatter,
                          LengthLimitingTextInputFormatter(8),
                          ...priceInputFormatter,
                        ],
                      ),
                    ),
                  ),
                  BlocBuilder<
                    ValidateConditionOrderCubit,
                    ValidateConditionOrderState
                  >(
                    buildWhen:
                        (previous, current) =>
                            previous.errorSlippage != current.errorSlippage ||
                            previous.currentSlippage != current.currentSlippage,
                    builder: (context, state) {
                      return InputFieldError(
                        errorMessage: state.errorSlippage.message,
                        text: widget.slippageController.text,
                        isShake: true,
                      );
                    },
                  ),
                  const SizedBox(height: 8),
                ],
              ),
            ),
          );
        },
      ),
    );
  }
}
