import 'package:equatable/equatable.dart';
import 'package:vp_common/error/show_error.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_trading/core/repository/command_history_repository.dart';
import 'package:vp_trading/model/order/request/condition_order_request_model.dart';

part 'edit_condition_order_state.dart';

class EditConditionOrderCubit extends Cubit<EditConditionOrderState> {
  EditConditionOrderCubit() : super(const EditConditionOrderState());

  final CommandHistoryRepository _commandHistoryRepository =
      GetIt.instance<CommandHistoryRepository>();

  void resetErrorMessage() {
    emit(state.copyWith(errorMessage: null));
  }

  void resetSuccessMessage() {
    emit(state.copyWith(successMessage: null));
  }

  /// Update condition order
  Future<void> updateConditionOrder({
    required ConditionOrderRequestModel request,
  }) async {
    try {
      if (isClosed) return;

      emit(
        state.copyWith(
          status: EditConditionOrderStatus.loading,
          errorMessage: null,
          successMessage: null,
        ),
      );

      final response = await _commandHistoryRepository.editConditonOrder(
        request,
      );
      if (!response.isSuccess) {
        emit(
          state.copyWith(
            status: EditConditionOrderStatus.failure,
            errorMessage: response.messageVi ?? response.message,
          ),
        );
        return;
      }
      emit(state.copyWith(status: EditConditionOrderStatus.success));
    } catch (error) {
      if (isClosed) return;
      var message = (await getErrorMessage(error));
      emit(
        state.copyWith(
          status: EditConditionOrderStatus.failure,
          errorMessage: message,
        ),
      );
    } finally {
      if (!isClosed) {
        emit(state.copyWith(status: EditConditionOrderStatus.initial));
      }
    }
  }
}
