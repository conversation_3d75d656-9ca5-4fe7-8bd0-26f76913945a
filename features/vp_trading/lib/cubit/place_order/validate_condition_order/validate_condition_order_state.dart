part of 'validate_condition_order_cubit.dart';

class ValidateConditionOrderState extends Equatable {
  final String? fromDate;
  final String? toDate;
  final ErrorTrigger errorTrigger;
  final ErrorPriceOrder errorPriceOrder;
  final ErrorSlippage errorSlippage;
  final ActivationConditionsType activationType;
  final TakeProfitTriggerConditionEnum triggerConditionEnum;
  final FocusKeyboard focusKeyboard;
  final String? currentTrigger;
  final String? currentSlippage;
  final StockInfoModel? stockInfo;
  final num? costPrice;
  final num? trade;
  final OrderType orderType;
  final bool isEditMode;

  const ValidateConditionOrderState({
    this.fromDate,
    this.toDate,
    this.currentTrigger,
    this.currentSlippage,
    this.stockInfo,
    this.costPrice,
    this.trade,
    this.orderType = OrderType.lo,
    this.errorTrigger = ErrorTrigger.init,
    this.errorPriceOrder = ErrorPriceOrder.init,
    this.errorSlippage = ErrorSlippage.init,
    this.activationType = ActivationConditionsType.lessThan,
    this.focusKeyboard = FocusKeyboard.none,
    this.triggerConditionEnum = TakeProfitTriggerConditionEnum.rateProfit,
    this.isEditMode = false,
  });

  @override
  List<Object?> get props => [
    fromDate,
    toDate,
    currentTrigger,
    currentSlippage,
    activationType,
    stockInfo,
    costPrice,
    trade,
    orderType,
    triggerConditionEnum,
    errorTrigger,
    errorSlippage,
    errorPriceOrder,
    focusKeyboard,
    isEditMode,
  ];

  ValidateConditionOrderState copyWith({
    String? fromDate,
    String? toDate,
    String? currentTrigger,
    String? currentSlippage,
    num? costPrice,
    num? trade,
    OrderType? orderType,
    StockInfoModel? stockInfo,
    ErrorTrigger? errorTrigger,
    ErrorPriceOrder? errorPriceOrder,
    ErrorSlippage? errorSlippage,
    FocusKeyboard? focusKeyboard,
    ActivationConditionsType? activationType,
    TakeProfitTriggerConditionEnum? triggerConditionEnum,
    bool? isEditMode,
  }) {
    return ValidateConditionOrderState(
      fromDate: fromDate ?? this.fromDate,
      toDate: toDate ?? this.toDate,
      stockInfo: stockInfo ?? this.stockInfo,
      costPrice: costPrice ?? this.costPrice,
      trade: trade ?? this.trade,
      orderType: orderType ?? this.orderType,
      currentTrigger: currentTrigger ?? this.currentTrigger,
      currentSlippage: currentSlippage ?? this.currentSlippage,
      errorTrigger: errorTrigger ?? this.errorTrigger,
      errorPriceOrder: errorPriceOrder ?? this.errorPriceOrder,
      errorSlippage: errorSlippage ?? this.errorSlippage,
      focusKeyboard: focusKeyboard ?? this.focusKeyboard,
      activationType: activationType ?? this.activationType,
      triggerConditionEnum: triggerConditionEnum ?? this.triggerConditionEnum,
      isEditMode: isEditMode ?? this.isEditMode,
    );
  }

  ValidateConditionOrderState clearSession() {
    return ValidateConditionOrderState(
      fromDate: fromDate,
      toDate: toDate,
      currentTrigger: currentTrigger,
      currentSlippage: currentSlippage,
      focusKeyboard: focusKeyboard,
      errorTrigger: errorTrigger,
      errorPriceOrder: errorPriceOrder,
      errorSlippage: errorSlippage,
      activationType: activationType,
      triggerConditionEnum: triggerConditionEnum,
      isEditMode: isEditMode,
    );
  }

  ValidateConditionOrderState clearData() {
    return ValidateConditionOrderState(
      fromDate: DateTime.now().formatToDdMmYyyy(),
      toDate: DateTime.now().formatToDdMmYyyy(),
      currentTrigger: null,
      currentSlippage: null,
      orderType: orderType,
      trade: trade,
      stockInfo: stockInfo,
      costPrice: costPrice,
      focusKeyboard: FocusKeyboard.none,
      errorTrigger: ErrorTrigger.init,
      errorSlippage: ErrorSlippage.init,
      errorPriceOrder: ErrorPriceOrder.init,
      triggerConditionEnum: TakeProfitTriggerConditionEnum.rateProfit,
      isEditMode: isEditMode,
    );
  }

  ValidateConditionOrderState clearDataTrigger() {
    return ValidateConditionOrderState(
      fromDate: fromDate,
      toDate: toDate,
      currentTrigger: null,
      trade: trade,
      stockInfo: stockInfo,
      orderType: orderType,
      currentSlippage: currentSlippage,
      focusKeyboard: focusKeyboard,
      costPrice: costPrice,
      errorTrigger: errorTrigger,
      errorSlippage: errorSlippage,
      errorPriceOrder: errorPriceOrder,
      triggerConditionEnum: triggerConditionEnum,
      isEditMode: isEditMode,
    );
  }
}

extension ValidateOrderStateExtension on ValidateConditionOrderState {
  bool get isErrorTrigger => errorTrigger != ErrorTrigger.none;

  bool get isErrorPriceOrder => errorPriceOrder != ErrorPriceOrder.none;

  bool get isErrorSlippage => errorSlippage != ErrorSlippage.none;

  bool get isEmptyOrderPrice =>
      currentTrigger.isNullOrEmpty && currentSlippage.isNullOrEmpty;

  bool get isRateProfit =>
      triggerConditionEnum == TakeProfitTriggerConditionEnum.rateProfit;

  num get orderPrice => _getOrderPrice();

  num get orderPriceAbs => orderPrice.formatConditionalPriceToRequest(
    stockInfo?.stockType,
    stockInfo?.exchangeCode,
  );

  String get orderPriceDisplay => _getOrderPriceDisplay();

  String get costPriceDisplay => _getCostPriceDisplay();

  bool get isHoldSymbol => (trade != null && !((trade ?? -1) < 0));

  String _getOrderPriceDisplay() {
    if (currentTrigger.isNullOrEmpty && currentSlippage.isNullOrEmpty) {
      return '--';
    }
    if (!isHoldSymbol) {
      return '--';
    } else {
      return orderPrice
          .getValidPriceForConditionalRequest(
            stockInfo?.stockType,
            stockInfo?.exchangeCode,
          )
          .toString();
    }
  }

  String _getCostPriceDisplay() {
    if (!isHoldSymbol) {
      return '--';
    } else {
      return (costPrice ?? 0).toDouble().getPriceFormatted(
        convertToThousand: true,
      );
    }
  }

  num _getOrderPrice() {
    if (orderType == OrderType.takeProfit) {
      return _getTakeProfitOrderPrice();
    } else if (orderType == OrderType.stopLoss) {
      return _getStopLossOrderPrice();
    } else {
      return 0;
    }
  }

  double get rateProfit => double.tryParse(currentTrigger ?? '0') ?? 0;

  double get slippagePrice => double.tryParse(currentSlippage ?? '0') ?? 0;

  num _getTakeProfitOrderPrice() {
    final costPriceValue = (costPrice ?? 0).toInt();
    num orderPrice = 0;
    if (isRateProfit) {
      orderPrice = (costPriceValue * (1 + rateProfit / 100)).toInt();
    } else {
      orderPrice = (costPriceValue + (rateProfit * 1000)).toInt();
    }
    orderPrice = orderPrice - (slippagePrice * 1000);
    return orderPrice.toValidValueRequest();
  }

  num _getStopLossOrderPrice() {
    final costPriceValue = (costPrice ?? 0).toInt();
    num orderPrice = 0;
    if (isRateProfit) {
      orderPrice = costPriceValue * (1 - rateProfit / 100);
    } else {
      orderPrice = costPriceValue - (rateProfit * 1000);
    }
    orderPrice = orderPrice - (slippagePrice * 1000);

    return orderPrice;
  }
}
