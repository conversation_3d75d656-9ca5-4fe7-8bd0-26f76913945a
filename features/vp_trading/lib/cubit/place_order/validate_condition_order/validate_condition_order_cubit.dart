import 'package:flutter/material.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_stock_common/model/stock_info_model.dart';
import 'package:vp_trading/core/constant/constants.dart';
import 'package:vp_trading/core/constant/order_type.dart';
import 'package:vp_trading/core/extension/ext.dart';
import 'package:vp_trading/cubit/place_order/validate_order/validate_order_cubit.dart';
import 'package:vp_trading/generated/l10n.dart';
import 'package:vp_trading/model/enum/activation_conditions_type.dart';
import 'package:vp_trading/model/enum/take_profit_trigger_condition_enum.dart';
import 'package:vp_trading/model/holding_portfolio/holding_portfolio_stock_model.dart';
import 'package:vp_trading/utils/condition_command_util.dart';

part 'validate_condition_order_state.dart';

enum ErrorTrigger {
  invalidRate,
  invalidSlippage,
  none,
  empty,
  init,
  orderEmptyRate,
  orderEmptySlippage,
}

enum ErrorSlippage { invalid, none, empty, init }

enum ErrorPriceOrder { invalid, none, init }

extension ErrorTriggerExtension on ErrorTrigger {
  bool get isError {
    return this != ErrorTrigger.none &&
        this != ErrorTrigger.empty &&
        this != ErrorTrigger.init;
  }

  bool get isEditModeError {
    return this != ErrorTrigger.none && this != ErrorTrigger.init;
  }

  String message(bool isStopLos) {
    switch (this) {
      case ErrorTrigger.invalidRate:
        return 'Tỷ lệ ${isStopLos ? 'cắt lỗ' : 'chốt lời'} không hợp lệ';
      case ErrorTrigger.invalidSlippage:
        return 'Biên giá ${isStopLos ? 'cắt lỗ' : 'chốt lời'} không hợp lệ';
      case ErrorTrigger.orderEmptyRate:
        return 'Vui lòng nhập Tỷ lệ ${isStopLos ? 'cắt lỗ' : 'chốt lời'}';
      case ErrorTrigger.orderEmptySlippage:
        return 'Vui lòng nhập Biên giá ${isStopLos ? 'cắt lỗ' : 'chốt lời'}';
      case ErrorTrigger.none:
        return '';
      case ErrorTrigger.empty:
        return 'Vui lòng nhập ${isStopLos ? 'Tỷ lệ cắt lỗ' : 'Tỷ lệ chốt lời'}';
      case ErrorTrigger.init:
        return '';
    }
  }
}

extension ErrorSlippageExtension on ErrorSlippage {
  bool get isError {
    return this != ErrorSlippage.none &&
        this != ErrorSlippage.empty &&
        this != ErrorSlippage.init;
  }

  bool get isEditModeError {
    return this != ErrorSlippage.none && this != ErrorSlippage.init;
  }

  String get message {
    switch (this) {
      case ErrorSlippage.invalid:
        return 'Biên trượt giá không hợp lệ';
      case ErrorSlippage.none:
        return '';
      case ErrorSlippage.empty:
        return 'Vui lòng nhập Biên trượt giá';
      case ErrorSlippage.init:
        return '';
    }
  }
}

extension ErrorPriceOrderExtension on ErrorPriceOrder {
  bool get isError {
    return this != ErrorPriceOrder.none && this != ErrorPriceOrder.init;
  }

  String get message {
    switch (this) {
      case ErrorPriceOrder.invalid:
        return VPTradingLocalize.current.trading_order_price_invalid;
      case ErrorPriceOrder.none:
        return '';
      case ErrorPriceOrder.init:
        return '';
    }
  }
}

class ValidateConditionOrderCubit extends Cubit<ValidateConditionOrderState> {
  ValidateConditionOrderCubit() : super(const ValidateConditionOrderState());
  OrderType _orderType = OrderType.lo;
  StockInfoModel? _stockInfo;
  BuildContext? _context;
  bool _isEditMode = false;

  bool get isLoOrGtcOrBuyIn =>
      _orderType.isLoOrGtc || _orderType == OrderType.buyIn;

  bool get isEditMode => _isEditMode;

  void updateParam({
    OrderType? orderType,
    StockInfoModel? stockInfo,
    HoldingPortfolioStockModel? portfolioStockModel,
    HoldingPortfolioStockModel? portfolioStockWftModel,
    BuildContext? context,
    bool? isEditMode,
  }) {
    if (context != null) {
      _context = context;
    }
    if (stockInfo != null) {
      _stockInfo = stockInfo;
    }
    if (orderType != null) {
      _orderType = orderType;
    }
    if (isEditMode != null) {
      _isEditMode = isEditMode;
    }

    var costPriceRoot =
        (portfolioStockModel?.costPrice ?? 0) *
        (portfolioStockModel?.total ?? 0);
    var costPriceWft =
        (portfolioStockWftModel?.costPrice ?? 0) *
        (portfolioStockWftModel?.total ?? 0);
    var total =
        (portfolioStockModel?.total ?? 0) +
        (portfolioStockWftModel?.total ?? 0);
    var costPrice = (costPriceRoot + costPriceWft) / total;
    if (isEditMode ?? false) {
      emit(
        state.copyWith(
          stockInfo: stockInfo,
          orderType: orderType,
          trade: portfolioStockModel?.trade,
          costPrice: costPrice.isNaN ? 0 : costPrice,
        ),
      );
    } else {
      emit(
        state.copyWith(
          stockInfo: stockInfo,
          orderType: orderType,
          trade: portfolioStockModel?.trade,
          costPrice: costPrice.isNaN ? 0 : costPrice,
          fromDate: DateTime.now().formatToDdMmYyyy(),
          toDate: DateTime.now().formatToDdMmYyyy(),
        ),
      );
    }
  }

  void focusField(FocusKeyboard type) {
    emit(state.copyWith(focusKeyboard: type));
  }

  void clearSession() {
    emit(state.clearSession());
  }

  void clearData() {
    emit(state.clearData());
    onChangeTrigger('');
    onChangeSlippage('');
  }

  void setEffectiveTime(DateTime fromDate, DateTime toDate) {
    emit(
      state.copyWith(
        fromDate: fromDate.formatToDdMmYyyy(),
        toDate: toDate.formatToDdMmYyyy(),
      ),
    );
  }

  void setActivationConditions(ActivationConditionsType activationType) {
    emit(state.copyWith(activationType: activationType));
  }

  void setTriggerConditionEnum(
    TakeProfitTriggerConditionEnum triggerConditionEnum,
  ) {
    emit(state.copyWith(triggerConditionEnum: triggerConditionEnum));
    emit(state.clearDataTrigger());
    onChangeTrigger('');
  }

  void setCostPrice(num costPrice) {
    emit(state.copyWith(costPrice: costPrice));
  }

  void setTrade(num trade) {
    emit(state.copyWith(trade: trade));
  }

  void onChangeTrigger(String value, {bool isOrder = false}) {
    final error = validateTrigger(text: value, isOrder: isOrder);

    emit(state.copyWith(errorTrigger: error, currentTrigger: value));
    onChangePriceOrder();
    _context?.read<ValidateOrderCubit>().onUpdateCalculateValue();
  }

  void onChangeSlippage(String value) {
    final error = validateSlippage(text: value);

    emit(state.copyWith(errorSlippage: error, currentSlippage: value));
    onChangePriceOrder();
    _context?.read<ValidateOrderCubit>().onUpdateCalculateValue();
  }

  triggerTap({required String text, bool increase = true}) {
    String onTap({required String text, bool increase = true}) {
      final newText = ConditionCommandUtil.updateValueTrigger(
        increase,
        text.price ?? 0.0,
        _stepTrigger,
        state.isRateProfit,
      ).toDouble().getPriceFormatted(convertToThousand: true);
      if (newText.length > 8) return text;
      return newText;
    }

    final newText = onTap(text: text, increase: increase);
    onChangeTrigger(newText);
  }

  void slippageTap({required String text, bool increase = true}) {
    String onTap({required String text, bool increase = true}) {
      final newText = ConditionCommandUtil.updateValueSlippage(
        increase,
        text.price ?? 0.0,
        _stepSlippage,
      ).toDouble().getPriceFormatted(convertToThousand: true);
      if (newText.length > 8) return text;
      return newText;
    }

    final newText = onTap(text: text, increase: increase);
    onChangeSlippage(newText);
  }

  double get _stepSlippage => 100;

  double get _maxTakeProfitRate => 300;

  double get _stepTrigger {
    if (state.isRateProfit) {
      return 1000;
    } else {
      return _stepSlippage;
    }
  }

  ErrorTrigger validateTrigger({required String text, bool isOrder = false}) {
    if (_stockInfo == null) return ErrorTrigger.none;

    if (isOrder && text.isEmpty) {
      if (state.isRateProfit) {
        return ErrorTrigger.orderEmptyRate;
      }
      return ErrorTrigger.orderEmptySlippage;
    }

    if (text.isEmpty) return ErrorTrigger.empty;

    final double? price = double.tryParse(text);
    if (price == null || price <= 0) return errorTrigger;

    if (state.isRateProfit) {
      if (price > _maxTakeProfitRate) {
        return errorTrigger;
      }
    }

    return ErrorTrigger.none;
  }

  ErrorTrigger get errorTrigger =>
      state.isRateProfit
          ? ErrorTrigger.invalidRate
          : ErrorTrigger.invalidSlippage;

  ErrorSlippage validateSlippage({required String text}) {
    if (_stockInfo == null) return ErrorSlippage.none;

    if (text.isEmpty) return ErrorSlippage.empty;

    if ((text.price ?? 0.0) < 0.0) return ErrorSlippage.invalid;
    return ErrorSlippage.none;
  }

  void onChangePriceOrder() {
    final error = validatePriceOrder();

    emit(state.copyWith(errorPriceOrder: error));
  }

  ErrorPriceOrder validatePriceOrder() {
    if (_stockInfo == null) return ErrorPriceOrder.none;
    if (state.isEmptyOrderPrice) return ErrorPriceOrder.none;

    if (state.orderPriceAbs <= 0) return ErrorPriceOrder.invalid;
    if (state.orderPriceAbs <= (state.costPrice ?? 0) &&
        _orderType.isTakeProfit) {
      return ErrorPriceOrder.invalid;
    }

    if (state.orderPriceAbs >= (state.costPrice ?? 0) &&
        _orderType.isStopLoss) {
      return ErrorPriceOrder.invalid;
    }
    return ErrorPriceOrder.none;
  }
}
