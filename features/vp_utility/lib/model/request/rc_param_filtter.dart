import 'package:flutter/material.dart';
import 'package:json_annotation/json_annotation.dart';
import 'package:vp_utility/core/constant/date_filter_constants.dart';
import 'package:vp_utility/screen/recommendation_list/enum/recommendation_enum.dart';

part 'rc_param_filtter.g.dart';

class RcParamFilter {
  final String? symbol;
  final MarketType? marketTypeInit;
  final RecommendationTypeFilter? recommendationTypeInit;
  final RecommendationStatus? statusTypeInit;
  final DateTimeRange? dateTimeRangeCustom;

  RcParamFilter({
    this.symbol,
    this.marketTypeInit,
    this.recommendationTypeInit,
    this.statusTypeInit,
    this.dateTimeRangeCustom,
  });

  bool isDefault() {
    return (marketTypeInit == null || marketTypeInit == MarketType.all) &&
        (recommendationTypeInit == null ||
            recommendationTypeInit == RecommendationTypeFilter.all) &&
        (statusTypeInit == null ||
            statusTypeInit == RecommendationStatus.all) &&
        (dateTimeRangeCustom == null ||
            DateFilterConstants.isDefaultDateRange(dateTimeRangeCustom)) &&
        (symbol == null);
  }

  /// Creates a filter with default 1-month date range
  factory RcParamFilter.withDefaultDateRange() {
    return RcParamFilter(
      dateTimeRangeCustom: DateFilterConstants.defaultDateRange,
    );
  }

  /// Checks if this filter has a custom date range (different from default)
  bool hasCustomDateRange() {
    return dateTimeRangeCustom != null &&
        !DateFilterConstants.isDefaultDateRange(dateTimeRangeCustom);
  }

  /// Gets the effective date range (custom or default)
  DateTimeRange getEffectiveDateRange() {
    return dateTimeRangeCustom ?? DateFilterConstants.defaultDateRange;
  }
}

@JsonSerializable(includeIfNull: false)
class RcRequestParam {
  final String? dept;

  final String? symbols;

  final int? pageNo;

  final int? pageSize;

  final String? exchangeCode;

  final String? typeRc;

  final String? statusRc;

  final String? fromDate;

  final String? toDate;

  RcRequestParam({
    this.dept,
    this.symbols,
    this.pageNo,
    this.pageSize,
    this.exchangeCode,
    this.typeRc,
    this.statusRc,
    this.fromDate,
    this.toDate,
  });

  factory RcRequestParam.fromJson(Map<String, dynamic> json) =>
      _$RcRequestParamFromJson(json);
  Map<String, dynamic> toJson() => _$RcRequestParamToJson(this);
}
