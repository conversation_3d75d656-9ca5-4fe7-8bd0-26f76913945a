// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'rc_param_filtter.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

RcRequestParam _$RcRequestParamFromJson(Map<String, dynamic> json) =>
    RcRequestParam(
      dept: json['dept'] as String?,
      symbols: json['symbols'] as String?,
      pageNo: (json['pageNo'] as num?)?.toInt(),
      pageSize: (json['pageSize'] as num?)?.toInt(),
      exchangeCode: json['exchangeCode'] as String?,
      typeRc: json['typeRc'] as String?,
      statusRc: json['statusRc'] as String?,
      fromDate: json['fromDate'] as String?,
      toDate: json['toDate'] as String?,
    );

Map<String, dynamic> _$RcRequestParamToJson(RcRequestParam instance) =>
    <String, dynamic>{
      if (instance.dept case final value?) 'dept': value,
      if (instance.symbols case final value?) 'symbols': value,
      if (instance.pageNo case final value?) 'pageNo': value,
      if (instance.pageSize case final value?) 'pageSize': value,
      if (instance.exchangeCode case final value?) 'exchangeCode': value,
      if (instance.typeRc case final value?) 'typeRc': value,
      if (instance.statusRc case final value?) 'statusRc': value,
      if (instance.fromDate case final value?) 'fromDate': value,
      if (instance.toDate case final value?) 'toDate': value,
    };
