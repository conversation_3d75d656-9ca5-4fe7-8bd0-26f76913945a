import 'package:flutter/material.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_utility/generated/l10n.dart';
import 'package:vp_utility/model/response/recommendation/recommendation_info_model.dart';
import 'package:vp_utility/screen/recommendation_home/enum/type_rc_enum.dart';
import 'package:vp_utility/screen/recommendation_home/widget/rc_info_bottom_sheet.dart';

class ItemRcWidget extends StatelessWidget {
  const ItemRcWidget({
    super.key,
    required this.model,
    required this.visibleTime,
    required this.isNCP,
  });

  final RecommendationInfoModel model;
  final bool visibleTime;
  final bool isNCP;

  @override
  Widget build(BuildContext context) {
    final colorUtils = Theme.of(context);
    return InkWell(
      onTap:
          () =>
              showRcInfoBottomSheet(context, model, isHome: true, isNCP: isNCP),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Visibility(
              visible: visibleTime,
              child: Padding(
                padding: const EdgeInsets.only(top: 8),
                child: Text(
                  AppTimeUtils.getDateTimeString(dateTime: model.from),
                  style: context.textStyle.captionRegular?.copyWith(
                    color: colorUtils.borderPopUp,
                  ),
                ),
              ),
            ),
            Padding(
              padding: const EdgeInsets.symmetric(vertical: 16),
              child: Row(
                children: [
                  Expanded(
                    flex: 1,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          model.symbol ?? '',
                          style: context.textStyle.subtitle14?.copyWith(
                            color: vpColor.textPrimary,
                          ),
                        ),
                        Text(
                          "${model.getValuePrice(model.expectPriceBmin)} - ${model.getValuePrice(model.expectPriceBmax)}",
                          style: context.textStyle.body14?.copyWith(
                            color: vpColor.textPrimary,
                          ),
                        ),
                      ],
                    ),
                  ),
                  SizedBox(width: 16),

                  Text(
                    model.typeRcEnum.localizedText.toUpperCase(),
                    style: context.textStyle.captionSemiBold?.copyWith(
                      color: model.typeRcEnum.color,
                    ),
                  ),
                  Expanded(
                    flex: 2,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.end,
                      children: [
                        Container(
                          padding: const EdgeInsets.all(4).copyWith(bottom: 6),
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(4),
                            color:
                                model.active
                                    ? colorUtils.primary16
                                    : colorUtils.red16,
                          ),
                          child: Text(
                            model.active
                                ? VpUtilityLocalize.current.rc_active
                                : VpUtilityLocalize.current.rc_status_expired,
                            style: context.textStyle.captionRegular?.copyWith(
                              color: vpColor.textPrimary,
                            ),
                          ),
                        ),
                        Text(
                          "${VpUtilityLocalize.current.rc_expire}: ${AppTimeUtils.parseStringToString(model.toDate, AppTimeUtilsFormat.dateYMD, AppTimeUtilsFormat.dateNormal)}",
                          style: context.textStyle.captionRegular?.copyWith(
                            color: colorUtils.gray500,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
